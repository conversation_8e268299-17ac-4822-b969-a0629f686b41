# Demos RAG Server

A TypeScript Node.js server for uploading and managing documents in Chroma vector database for RAG (Retrieval-Augmented Generation) applications. This server provides endpoints to upload documents from demo folders and clear collections in Chroma.

## Features

- **TypeScript**: Full TypeScript support with strict type checking
- **Document Upload**: Process and upload documents from demo folders to Chroma collections
- **Collection Management**: Clear or delete Chroma collections
- **Multiple File Formats**: Support for .txt, .md, .pdf, and .docx files
- **LangChain Integration**: Uses LangChain for optimal document splitting and processing
- **Configuration Management**: Automatic config file creation and management for each demo
- **Health Monitoring**: Health check endpoints for system status
- **Type Safety**: Comprehensive type definitions for all API endpoints and data structures

## Project Structure

```
demos-rag/
├── demos/                    # Demo folders
│   └── sample-demo/
│       └── documents/        # Documents to be processed
│           └── sample.txt
├── config/                   # Auto-generated config files
├── src/
│   ├── config/
│   │   └── configManager.ts  # Configuration management
│   ├── services/
│   │   ├── documentProcessor.ts  # Document processing with <PERSON><PERSON><PERSON><PERSON>
│   │   └── chromaService.ts      # Chroma database operations
│   └── types/
│       └── index.ts          # TypeScript type definitions
├── server.ts                 # Main server file
├── tsconfig.json             # TypeScript configuration
├── dist/                     # Compiled JavaScript output
├── package.json
└── .env                      # Environment configuration
```

## Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Configure the following variables:

```env
# Chroma Configuration
CHROMA_URL=http://localhost:8000
# Or use your hosted Chroma instance:
# CHROMA_URL=https://your-hosted-chroma-instance.com

# OpenAI API Key (for embeddings)
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=3000
```

### 3. Create Demo Structure

Create your demo folders in the `demos` directory:

```bash
mkdir -p demos/your-demo-name/documents
```

Add your documents to the `documents` folder. Supported formats:
- `.txt` - Plain text files
- `.md` - Markdown files
- `.pdf` - PDF documents
- `.docx` - Word documents

## Usage

### Build and Start the Server

```bash
# Build TypeScript to JavaScript
npm run build

# Production (run compiled JavaScript)
npm start

# Development (run TypeScript directly with auto-reload)
npm run dev

# Development with file watching
npm run dev:watch

# Type checking only (no compilation)
npm run type-check
```

The server will start on `http://localhost:3000` (or the port specified in your `.env` file).

## API Endpoints

### POST /upload

Upload documents from a demo folder to Chroma.

**Request Body:**
```json
{
  "demoName": "sample-demo",
  "chunkSize": 1000,        // Optional, default: 1000
  "chunkOverlap": 200,      // Optional, default: 200
  "collectionName": "custom_collection_name"  // Optional, auto-generated if not provided
}
```

**Response:**
```json
{
  "success": true,
  "demoName": "sample-demo",
  "config": {
    "collectionName": "demo_sample-demo_1640995200000",
    "chunkSize": 1000,
    "chunkOverlap": 200
  },
  "result": {
    "success": true,
    "collectionName": "demo_sample-demo_1640995200000",
    "documentCount": 5,
    "originalDocuments": 2,
    "chunks": 5,
    "message": "Successfully uploaded 5 document chunks to Chroma collection 'demo_sample-demo_1640995200000'"
  }
}
```

### POST /clear

Clear all documents from a demo's Chroma collection.

**Request Body:**
```json
{
  "demoName": "sample-demo"
}
```

**Response:**
```json
{
  "success": true,
  "demoName": "sample-demo",
  "collectionName": "demo_sample-demo_1640995200000",
  "result": {
    "success": true,
    "collectionName": "demo_sample-demo_1640995200000",
    "deletedCount": 5,
    "message": "Successfully cleared 5 documents from collection 'demo_sample-demo_1640995200000'"
  }
}
```

### GET /demos

List all available demos and their status.

**Response:**
```json
{
  "success": true,
  "demos": [
    {
      "name": "sample-demo",
      "hasDocuments": true,
      "config": {
        "demoName": "sample-demo",
        "collectionName": "demo_sample-demo_1640995200000",
        "documentCount": 2,
        "chunkCount": 5
      },
      "collectionInfo": {
        "name": "demo_sample-demo_1640995200000",
        "documentCount": 5,
        "exists": true
      }
    }
  ]
}
```

### GET /health

Health check endpoint.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "chroma": {
    "status": "healthy",
    "url": "http://localhost:8000",
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

## Configuration Files

The server automatically creates configuration files for each demo in the `config/` directory. These files contain:

- Collection name in Chroma
- Document processing settings (chunk size, overlap)
- Upload timestamps and document counts
- Chroma URL and other settings

Example config file (`config/sample-demo.json`):
```json
{
  "demoName": "sample-demo",
  "collectionName": "demo_sample-demo_1640995200000",
  "chromaUrl": "http://localhost:8000",
  "createdAt": "2024-01-01T12:00:00.000Z",
  "updatedAt": "2024-01-01T12:00:00.000Z",
  "documentCount": 2,
  "chunkCount": 5,
  "chunkSize": 1000,
  "chunkOverlap": 200,
  "lastUpload": "2024-01-01T12:00:00.000Z"
}
```

## Error Handling

The server includes comprehensive error handling for:

- Missing demo folders or documents
- Chroma connection issues
- Invalid file formats
- Configuration errors
- Network timeouts

All errors are returned in a consistent format:
```json
{
  "success": false,
  "message": "Error description",
  "demoName": "demo-name-if-applicable"
}
```

## TypeScript Support

This project is fully written in TypeScript with comprehensive type definitions:

- **Type Safety**: All API endpoints, request/response objects, and service methods are fully typed
- **Interface Definitions**: Complete type definitions in `src/types/index.ts`
- **Strict Configuration**: TypeScript strict mode enabled for maximum type safety
- **Development Experience**: Full IntelliSense support and compile-time error checking

### Key Type Interfaces

- `DemoConfig`: Configuration for each demo
- `UploadRequest/UploadResponse`: Upload endpoint types
- `ClearRequest/ClearResponse`: Clear endpoint types
- `ProcessingResult`: Document processing results
- `ChromaCollectionInfo`: Chroma collection metadata

## Dependencies

- **Express**: Web server framework
- **LangChain**: Document processing and text splitting
- **ChromaDB**: Vector database client
- **OpenAI**: Embeddings generation
- **fs-extra**: Enhanced file system operations
- **cors**: Cross-origin resource sharing
- **dotenv**: Environment variable management
- **TypeScript**: Static type checking and compilation

## Development

The project uses modern JavaScript features and follows best practices for:

- Async/await error handling
- Modular service architecture
- Configuration management
- Logging and monitoring
- Input validation

## License

ISC

import 'dotenv/config';
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import * as path from 'path';
import * as fs from 'fs-extra';

import { ConfigManager } from './src/config/configManager';
import { DocumentProcessor } from './src/services/documentProcessor';
import { ChromaService } from './src/services/chromaService';
import {
  UploadRequest,
  ClearRequest,
  UploadResponse,
  ClearResponse,
  HealthCheckResponse,
  DemosListResponse,
  ApiResponse,
  TypedRequest,
  TypedResponse
} from './src/types';

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Initialize services
const configManager = new ConfigManager();
const documentProcessor = new DocumentProcessor();
const chromaService = new ChromaService();

// Utility function to validate demo exists
async function validateDemoExists(demoName: string): Promise<boolean> {
  const demoPath = path.join(process.cwd(), 'demos', demoName);
  const documentsPath = path.join(demoPath, 'documents');

  if (!await fs.pathExists(demoPath)) {
    throw new Error(`Demo '${demoName}' does not exist`);
  }

  if (!await fs.pathExists(documentsPath)) {
    throw new Error(`Documents folder not found for demo '${demoName}'`);
  }

  return true;
}

// Health check endpoint
app.get('/health', async (req: Request, res: TypedResponse<HealthCheckResponse>) => {
  try {
    const chromaHealth = await chromaService.healthCheck();
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      chroma: chromaHealth
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({
      status: 'error',
      message: errorMessage,
      timestamp: new Date().toISOString(),
      chroma: {
        status: 'unhealthy',
        url: chromaService['chromaUrl'] || 'unknown',
        error: errorMessage,
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Upload endpoint
app.post('/upload', async (req: Request, res: Response) => {
  try {
    const { demoName, chunkSize, chunkOverlap, collectionName } = req.body;

    if (!demoName) {
      return res.status(400).json({
        success: false,
        message: 'Demo name is required'
      });
    }

    console.log(`Upload request for demo: ${demoName}`);

    // Validate demo exists
    await validateDemoExists(demoName);

    // Check if config exists, create if not
    let config;
    if (await configManager.configExists(demoName)) {
      config = await configManager.getDemoConfig(demoName);
      console.log(`Using existing config for demo: ${demoName}`);
    } else {
      config = await configManager.createDemoConfig(demoName, {
        chunkSize: chunkSize || 1000,
        chunkOverlap: chunkOverlap || 200,
        collectionName: collectionName || `demo_${demoName}_${Date.now()}`
      });
      console.log(`Created new config for demo: ${demoName}`);
    }

    // Process and upload documents
    const result = await documentProcessor.processAndUpload(demoName, config);

    // Update config with document count
    await configManager.updateDemoConfig(demoName, {
      documentCount: result.originalDocuments,
      chunkCount: result.chunks,
      lastUpload: new Date().toISOString()
    });

    res.json({
      success: true,
      demoName,
      config: {
        collectionName: config.collectionName,
        chunkSize: config.chunkSize,
        chunkOverlap: config.chunkOverlap
      },
      result
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: errorMessage,
      demoName: req.body.demoName
    });
  }
});

// Clear endpoint
app.post('/clear', async (req: TypedRequest<ClearRequest>, res: TypedResponse<ClearResponse>) => {
  try {
    const { demoName } = req.body;

    if (!demoName) {
      return res.status(400).json({
        success: false,
        message: 'Demo name is required'
      });
    }

    console.log(`Clear request for demo: ${demoName}`);

    // Get demo config to find collection name
    if (!await configManager.configExists(demoName)) {
      return res.status(404).json({
        success: false,
        message: `Configuration for demo '${demoName}' not found. Demo may not have been uploaded yet.`
      });
    }

    const config = await configManager.getDemoConfig(demoName);

    // Clear the collection
    const result = await chromaService.clearCollection(config.collectionName);

    // Update config
    await configManager.updateDemoConfig(demoName, {
      documentCount: 0,
      chunkCount: 0,
      lastCleared: new Date().toISOString()
    });

    res.json({
      success: true,
      demoName,
      collectionName: config.collectionName,
      result
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Clear error:', error);
    res.status(500).json({
      success: false,
      message: errorMessage,
      demoName: req.body.demoName
    });
  }
});

// List demos endpoint (bonus)
app.get('/demos', async (req: Request, res: TypedResponse<DemosListResponse>) => {
  try {
    const demosPath = path.join(process.cwd(), 'demos');
    const demoFolders = await fs.readdir(demosPath);

    const demos = [];
    for (const folder of demoFolders) {
      const demoPath = path.join(demosPath, folder);
      const stat = await fs.stat(demoPath);

      if (stat.isDirectory()) {
        const documentsPath = path.join(demoPath, 'documents');
        const hasDocuments = await fs.pathExists(documentsPath);

        let config = null;
        let collectionInfo = null;

        if (await configManager.configExists(folder)) {
          config = await configManager.getDemoConfig(folder);
          collectionInfo = await chromaService.getCollectionInfo(config.collectionName);
        }

        demos.push({
          name: folder,
          hasDocuments,
          config,
          collectionInfo
        });
      }
    }

    res.json({
      success: true,
      demos
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('List demos error:', error);
    res.status(500).json({
      success: false,
      message: errorMessage
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Chroma URL: ${process.env.CHROMA_URL || 'http://localhost:8000'}`);
  console.log('\nAvailable endpoints:');
  console.log('  POST /upload - Upload demo documents to Chroma');
  console.log('  POST /clear  - Clear demo collection from Chroma');
  console.log('  GET  /demos  - List all demos and their status');
  console.log('  GET  /health - Health check');
});

export default app;

"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs-extra"));
const configManager_1 = require("./src/config/configManager");
const documentProcessor_1 = require("./src/services/documentProcessor");
const chromaService_1 = require("./src/services/chromaService");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(express_1.default.urlencoded({ extended: true }));
// Initialize services
const configManager = new configManager_1.ConfigManager();
const documentProcessor = new documentProcessor_1.DocumentProcessor();
const chromaService = new chromaService_1.ChromaService();
// Utility function to validate demo exists
async function validateDemoExists(demoName) {
    const demoPath = path.join(process.cwd(), 'demos', demoName);
    const documentsPath = path.join(demoPath, 'documents');
    if (!await fs.pathExists(demoPath)) {
        throw new Error(`Demo '${demoName}' does not exist`);
    }
    if (!await fs.pathExists(documentsPath)) {
        throw new Error(`Documents folder not found for demo '${demoName}'`);
    }
    return true;
}
// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        const chromaHealth = await chromaService.healthCheck();
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            chroma: chromaHealth
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(500).json({
            status: 'error',
            message: errorMessage,
            timestamp: new Date().toISOString(),
            chroma: {
                status: 'unhealthy',
                url: chromaService['chromaUrl'] || 'unknown',
                error: errorMessage,
                timestamp: new Date().toISOString()
            }
        });
    }
});
// Upload endpoint
app.post('/upload', async (req, res) => {
    try {
        const { demoName, chunkSize, chunkOverlap, collectionName } = req.body;
        if (!demoName) {
            return res.status(400).json({
                success: false,
                message: 'Demo name is required'
            });
        }
        console.log(`Upload request for demo: ${demoName}`);
        // Validate demo exists
        await validateDemoExists(demoName);
        // Check if config exists, create if not
        let config;
        if (await configManager.configExists(demoName)) {
            config = await configManager.getDemoConfig(demoName);
            console.log(`Using existing config for demo: ${demoName}`);
        }
        else {
            config = await configManager.createDemoConfig(demoName, {
                chunkSize: chunkSize || 1000,
                chunkOverlap: chunkOverlap || 200,
                collectionName: collectionName || `demo_${demoName}_${Date.now()}`
            });
            console.log(`Created new config for demo: ${demoName}`);
        }
        // Process and upload documents
        const result = await documentProcessor.processAndUpload(demoName, config);
        // Update config with document count
        await configManager.updateDemoConfig(demoName, {
            documentCount: result.originalDocuments,
            chunkCount: result.chunks,
            lastUpload: new Date().toISOString()
        });
        res.json({
            success: true,
            demoName,
            config: {
                collectionName: config.collectionName,
                chunkSize: config.chunkSize,
                chunkOverlap: config.chunkOverlap
            },
            result
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Upload error:', error);
        res.status(500).json({
            success: false,
            message: errorMessage,
            demoName: req.body.demoName
        });
    }
});
// Clear endpoint
app.post('/clear', async (req, res) => {
    try {
        const { demoName } = req.body;
        if (!demoName) {
            return res.status(400).json({
                success: false,
                message: 'Demo name is required'
            });
        }
        console.log(`Clear request for demo: ${demoName}`);
        // Get demo config to find collection name
        if (!await configManager.configExists(demoName)) {
            return res.status(404).json({
                success: false,
                message: `Configuration for demo '${demoName}' not found. Demo may not have been uploaded yet.`
            });
        }
        const config = await configManager.getDemoConfig(demoName);
        // Clear the collection
        const result = await chromaService.clearCollection(config.collectionName);
        // Update config
        await configManager.updateDemoConfig(demoName, {
            documentCount: 0,
            chunkCount: 0,
            lastCleared: new Date().toISOString()
        });
        res.json({
            success: true,
            demoName,
            collectionName: config.collectionName,
            result
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Clear error:', error);
        res.status(500).json({
            success: false,
            message: errorMessage,
            demoName: req.body.demoName
        });
    }
});
// List demos endpoint (bonus)
app.get('/demos', async (req, res) => {
    try {
        const demosPath = path.join(process.cwd(), 'demos');
        const demoFolders = await fs.readdir(demosPath);
        const demos = [];
        for (const folder of demoFolders) {
            const demoPath = path.join(demosPath, folder);
            const stat = await fs.stat(demoPath);
            if (stat.isDirectory()) {
                const documentsPath = path.join(demoPath, 'documents');
                const hasDocuments = await fs.pathExists(documentsPath);
                let config = null;
                let collectionInfo = null;
                if (await configManager.configExists(folder)) {
                    config = await configManager.getDemoConfig(folder);
                    collectionInfo = await chromaService.getCollectionInfo(config.collectionName);
                }
                demos.push({
                    name: folder,
                    hasDocuments,
                    config,
                    collectionInfo
                });
            }
        }
        res.json({
            success: true,
            demos
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('List demos error:', error);
        res.status(500).json({
            success: false,
            message: errorMessage
        });
    }
});
// Start server
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log(`Chroma URL: ${process.env.CHROMA_URL || 'http://localhost:8000'}`);
    console.log('\nAvailable endpoints:');
    console.log('  POST /upload - Upload demo documents to Chroma');
    console.log('  POST /clear  - Clear demo collection from Chroma');
    console.log('  GET  /demos  - List all demos and their status');
    console.log('  GET  /health - Health check');
});
exports.default = app;
//# sourceMappingURL=server.js.map
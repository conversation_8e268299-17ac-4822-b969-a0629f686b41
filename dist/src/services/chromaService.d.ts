import { ChromaClearResult, ChromaCollectionInfo, ChromaHealthCheck } from '../types';
export declare class ChromaService {
    private readonly chromaUrl;
    private readonly client;
    constructor(chromaUrl?: string);
    clearCollection(collectionName: string): Promise<ChromaClearResult>;
    deleteCollection(collectionName: string): Promise<{
        success: boolean;
        collectionName: string;
        message: string;
    }>;
    listCollections(): Promise<any[]>;
    getCollectionInfo(collectionName: string): Promise<ChromaCollectionInfo>;
    healthCheck(): Promise<ChromaHealthCheck>;
}
//# sourceMappingURL=chromaService.d.ts.map
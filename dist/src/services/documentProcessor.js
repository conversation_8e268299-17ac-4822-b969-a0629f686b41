"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentProcessor = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const text_splitter_1 = require("langchain/text_splitter");
const directory_1 = require("langchain/document_loaders/fs/directory");
const text_1 = require("langchain/document_loaders/fs/text");
const openai_1 = require("@langchain/openai");
const chromadb_1 = require("chromadb");
class DocumentProcessor {
    constructor() {
        if (!process.env.OPENAI_API_KEY) {
            throw new Error('OPENAI_API_KEY environment variable is required');
        }
        this.embeddings = new openai_1.OpenAIEmbeddings({
            openAIApiKey: process.env.OPENAI_API_KEY,
        });
    }
    async loadDocumentsFromDemo(demoName) {
        const demoPath = path.join(process.cwd(), 'demos', demoName, 'documents');
        if (!await fs.pathExists(demoPath)) {
            throw new Error(`Demo documents directory not found: ${demoPath}`);
        }
        // Check if directory has any files
        const files = await fs.readdir(demoPath);
        if (files.length === 0) {
            throw new Error(`No documents found in demo '${demoName}'`);
        }
        // Create a directory loader with support for multiple file types
        const loader = new directory_1.DirectoryLoader(demoPath, {
            '.txt': (filePath) => new text_1.TextLoader(filePath),
            '.md': (filePath) => new text_1.TextLoader(filePath),
            // Note: PDF and DOCX loaders require additional setup
            // '.pdf': (filePath: string) => new PDFLoader(filePath),
            // '.docx': (filePath: string) => new DocxLoader(filePath),
        });
        const documents = await loader.load();
        if (documents.length === 0) {
            throw new Error(`No supported documents found in demo '${demoName}'. Supported formats: .txt, .md`);
        }
        // Add metadata to documents
        const typedDocuments = documents.map((doc, index) => ({
            ...doc,
            metadata: {
                ...doc.metadata,
                demoName,
                documentIndex: index,
                processedAt: new Date().toISOString(),
            }
        }));
        return typedDocuments;
    }
    async splitDocuments(documents, chunkSize = 1000, chunkOverlap = 200) {
        const textSplitter = new text_splitter_1.RecursiveCharacterTextSplitter({
            chunkSize,
            chunkOverlap,
        });
        const splitDocs = await textSplitter.splitDocuments(documents);
        // Add chunk metadata
        const typedSplitDocs = splitDocs.map((doc, index) => ({
            ...doc,
            metadata: {
                ...doc.metadata,
                chunkIndex: index,
                chunkSize: doc.pageContent.length,
            }
        }));
        return typedSplitDocs;
    }
    async uploadToChroma(documents, config) {
        try {
            // Initialize Chroma client
            const chromaClient = new chromadb_1.ChromaClient({
                path: config.chromaUrl,
            });
            // Create or get collection with explicit embedding function
            let collection;
            try {
                collection = await chromaClient.getCollection({
                    name: config.collectionName,
                });
                console.log(`Using existing collection: ${config.collectionName}`);
            }
            catch (error) {
                // Create collection without embedding function (we'll handle embeddings manually)
                collection = await chromaClient.createCollection({
                    name: config.collectionName,
                    metadata: { description: `Collection for demo: ${config.demoName}` }
                });
                console.log(`Created new collection: ${config.collectionName}`);
            }
            // Generate embeddings manually using OpenAI
            const texts = documents.map(doc => doc.pageContent);
            const embeddings = await this.embeddings.embedDocuments(texts);
            // Prepare data for Chroma
            const ids = documents.map((_, index) => `${config.demoName}_${Date.now()}_${index}`);
            const metadatas = documents.map(doc => ({
                ...doc.metadata,
                source: doc.metadata.source || 'unknown',
                demoName: config.demoName
            }));
            // Add documents to collection
            await collection.add({
                ids,
                embeddings,
                documents: texts,
                metadatas
            });
            console.log(`Successfully uploaded ${documents.length} documents to collection: ${config.collectionName}`);
            return {
                success: true,
                collectionName: config.collectionName,
                documentCount: documents.length,
                originalDocuments: 0, // Will be set by caller
                chunks: documents.length,
                message: `Successfully uploaded ${documents.length} document chunks to Chroma collection '${config.collectionName}'`
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error('Error uploading to Chroma:', error);
            // Check for specific error types
            if (errorMessage.includes('DefaultEmbeddingFunction') || errorMessage.includes('default-embed')) {
                throw new Error(`Failed to upload documents to Chroma: Missing embedding configuration. Make sure OPENAI_API_KEY is set and valid.`);
            }
            else if (errorMessage.includes('OPENAI_API_KEY')) {
                throw new Error(`Failed to upload documents to Chroma: OpenAI API key is invalid or missing.`);
            }
            else if (errorMessage.includes('Connection')) {
                throw new Error(`Failed to upload documents to Chroma: Cannot connect to Chroma server at ${config.chromaUrl}`);
            }
            throw new Error(`Failed to upload documents to Chroma: ${errorMessage}`);
        }
    }
    async processAndUpload(demoName, config) {
        try {
            console.log(`Starting document processing for demo: ${demoName}`);
            // Load documents
            const documents = await this.loadDocumentsFromDemo(demoName);
            console.log(`Loaded ${documents.length} documents`);
            // Split documents into chunks
            const splitDocs = await this.splitDocuments(documents, config.chunkSize, config.chunkOverlap);
            console.log(`Split into ${splitDocs.length} chunks`);
            // Upload to Chroma
            const result = await this.uploadToChroma(splitDocs, config);
            return {
                ...result,
                originalDocuments: documents.length,
                chunks: splitDocs.length,
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`Error processing documents for demo ${demoName}:`, errorMessage);
            throw error;
        }
    }
}
exports.DocumentProcessor = DocumentProcessor;
//# sourceMappingURL=documentProcessor.js.map
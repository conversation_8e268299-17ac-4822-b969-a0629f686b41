{"version": 3, "file": "documentProcessor.js", "sourceRoot": "", "sources": ["../../../src/services/documentProcessor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,2CAA6B;AAC7B,2DAAyE;AACzE,uEAA0E;AAC1E,6DAAgE;AAChE,8CAAqD;AACrD,qEAAkE;AAClE,uCAAwC;AAIxC,MAAa,iBAAiB;IAG5B;QACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,yBAAgB,CAAC;YACrC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;SACzC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QAE1E,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,mCAAmC;QACnC,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,QAAQ,GAAG,CAAC,CAAC;QAC9D,CAAC;QAED,iEAAiE;QACjE,MAAM,MAAM,GAAG,IAAI,2BAAe,CAAC,QAAQ,EAAE;YAC3C,MAAM,EAAE,CAAC,QAAgB,EAAE,EAAE,CAAC,IAAI,iBAAU,CAAC,QAAQ,CAAC;YACtD,KAAK,EAAE,CAAC,QAAgB,EAAE,EAAE,CAAC,IAAI,iBAAU,CAAC,QAAQ,CAAC;YACrD,sDAAsD;YACtD,yDAAyD;YACzD,2DAA2D;SAC5D,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QAEtC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,yCAAyC,QAAQ,iCAAiC,CAAC,CAAC;QACtG,CAAC;QAED,4BAA4B;QAC5B,MAAM,cAAc,GAAiC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAClF,GAAG,GAAG;YACN,QAAQ,EAAE;gBACR,GAAG,GAAG,CAAC,QAAQ;gBACf,QAAQ;gBACR,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAClB;SACtB,CAAC,CAAC,CAAC;QAEJ,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,SAAuC,EACvC,YAAoB,IAAI,EACxB,eAAuB,GAAG;QAE1B,MAAM,YAAY,GAAG,IAAI,8CAA8B,CAAC;YACtD,SAAS;YACT,YAAY;SACb,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAE/D,qBAAqB;QACrB,MAAM,cAAc,GAAiC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;YAClF,GAAG,GAAG;YACN,QAAQ,EAAE;gBACR,GAAG,GAAG,CAAC,QAAQ;gBACf,UAAU,EAAE,KAAK;gBACjB,SAAS,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM;aACd;SACtB,CAAC,CAAC,CAAC;QAEJ,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,SAAuC,EACvC,MAAkB;QAElB,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,YAAY,GAAG,IAAI,uBAAY,CAAC;gBACpC,IAAI,EAAE,MAAM,CAAC,SAAS;aACvB,CAAC,CAAC;YAEH,2BAA2B;YAC3B,IAAI,CAAC;gBACH,MAAM,YAAY,CAAC,aAAa,CAAC;oBAC/B,IAAI,EAAE,MAAM,CAAC,cAAc;iBAC5B,CAAC,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,8BAA8B,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YACrE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,CAAC,gBAAgB,CAAC;oBAClC,IAAI,EAAE,MAAM,CAAC,cAAc;iBAC5B,CAAC,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,sBAAsB;YACtB,MAAM,WAAW,GAAG,IAAI,eAAM,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC9C,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,GAAG,EAAE,MAAM,CAAC,SAAS;aACtB,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE,CAAC,CAAC;YAErF,MAAM,WAAW,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YAEnD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,aAAa,EAAE,SAAS,CAAC,MAAM;gBAC/B,iBAAiB,EAAE,CAAC,EAAE,wBAAwB;gBAC9C,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,OAAO,EAAE,yBAAyB,SAAS,CAAC,MAAM,0CAA0C,MAAM,CAAC,cAAc,GAAG;aACrH,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,yCAAyC,YAAY,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,MAAkB;QACzD,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,0CAA0C,QAAQ,EAAE,CAAC,CAAC;YAElE,iBAAiB;YACjB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,UAAU,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;YAEpD,8BAA8B;YAC9B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CACzC,SAAS,EACT,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,YAAY,CACpB,CAAC;YACF,OAAO,CAAC,GAAG,CAAC,cAAc,SAAS,CAAC,MAAM,SAAS,CAAC,CAAC;YAErD,mBAAmB;YACnB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAE5D,OAAO;gBACL,GAAG,MAAM;gBACT,iBAAiB,EAAE,SAAS,CAAC,MAAM;gBACnC,MAAM,EAAE,SAAS,CAAC,MAAM;aACzB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,OAAO,CAAC,KAAK,CAAC,uCAAuC,QAAQ,GAAG,EAAE,YAAY,CAAC,CAAC;YAChF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAjKD,8CAiKC"}
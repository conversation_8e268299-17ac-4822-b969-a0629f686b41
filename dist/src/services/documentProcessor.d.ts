import { Document } from 'langchain/document';
import { DemoConfig, ProcessingResult, DocumentMetadata } from '../types';
export declare class DocumentProcessor {
    private readonly embeddings;
    constructor();
    loadDocumentsFromDemo(demoName: string): Promise<Document<DocumentMetadata>[]>;
    splitDocuments(documents: Document<DocumentMetadata>[], chunkSize?: number, chunkOverlap?: number): Promise<Document<DocumentMetadata>[]>;
    uploadToChroma(documents: Document<DocumentMetadata>[], config: DemoConfig): Promise<ProcessingResult>;
    processAndUpload(demoName: string, config: DemoConfig): Promise<ProcessingResult>;
}
//# sourceMappingURL=documentProcessor.d.ts.map
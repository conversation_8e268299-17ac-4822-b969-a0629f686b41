import { DemoConfig, CreateDemoConfigOptions } from '../types';
export declare class ConfigManager {
    private readonly configDir;
    constructor();
    private ensureConfigDir;
    private getConfigPath;
    createDemoConfig(demoName: string, options?: CreateDemoConfigOptions): Promise<DemoConfig>;
    getDemoConfig(demoName: string): Promise<DemoConfig>;
    updateDemoConfig(demoName: string, updates: Partial<DemoConfig>): Promise<DemoConfig>;
    deleteDemoConfig(demoName: string): Promise<boolean>;
    listDemoConfigs(): Promise<DemoConfig[]>;
    configExists(demoName: string): Promise<boolean>;
}
//# sourceMappingURL=configManager.d.ts.map
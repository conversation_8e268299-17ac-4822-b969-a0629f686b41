"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
class ConfigManager {
    constructor() {
        this.configDir = path.join(process.cwd(), 'config');
        this.ensureConfigDir();
    }
    ensureConfigDir() {
        if (!fs.existsSync(this.configDir)) {
            fs.mkdirSync(this.configDir, { recursive: true });
        }
    }
    getConfigPath(demoName) {
        return path.join(this.configDir, `${demoName}.json`);
    }
    async createDemoConfig(demoName, options = {}) {
        const config = {
            demoName,
            collectionName: options.collectionName || `demo_${demoName}_${Date.now()}`,
            chromaUrl: options.chromaUrl || process.env.CHROMA_URL || 'http://localhost:8000',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            documentCount: 0,
            chunkSize: options.chunkSize || 1000,
            chunkOverlap: options.chunkOverlap || 200,
            ...options
        };
        const configPath = this.getConfigPath(demoName);
        await fs.writeJson(configPath, config, { spaces: 2 });
        return config;
    }
    async getDemoConfig(demoName) {
        const configPath = this.getConfigPath(demoName);
        if (!await fs.pathExists(configPath)) {
            throw new Error(`Configuration for demo '${demoName}' not found`);
        }
        return await fs.readJson(configPath);
    }
    async updateDemoConfig(demoName, updates) {
        const config = await this.getDemoConfig(demoName);
        const updatedConfig = {
            ...config,
            ...updates,
            updatedAt: new Date().toISOString()
        };
        const configPath = this.getConfigPath(demoName);
        await fs.writeJson(configPath, updatedConfig, { spaces: 2 });
        return updatedConfig;
    }
    async deleteDemoConfig(demoName) {
        const configPath = this.getConfigPath(demoName);
        if (await fs.pathExists(configPath)) {
            await fs.remove(configPath);
            return true;
        }
        return false;
    }
    async listDemoConfigs() {
        const files = await fs.readdir(this.configDir);
        const configFiles = files.filter(file => file.endsWith('.json'));
        const configs = [];
        for (const file of configFiles) {
            const demoName = path.basename(file, '.json');
            try {
                const config = await this.getDemoConfig(demoName);
                configs.push(config);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                console.warn(`Failed to read config for ${demoName}:`, errorMessage);
            }
        }
        return configs;
    }
    async configExists(demoName) {
        const configPath = this.getConfigPath(demoName);
        return await fs.pathExists(configPath);
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=configManager.js.map
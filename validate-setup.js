const fs = require('fs-extra');
const path = require('path');

async function validateSetup() {
  console.log('🔍 Validating Demos RAG Server Setup...\n');

  const checks = [];

  // Check 1: Required files exist
  const requiredFiles = [
    'server.js',
    'package.json',
    '.env.example',
    'README.md',
    'src/config/configManager.js',
    'src/services/documentProcessor.js',
    'src/services/chromaService.js'
  ];

  for (const file of requiredFiles) {
    const exists = await fs.pathExists(file);
    checks.push({
      name: `File exists: ${file}`,
      passed: exists,
      message: exists ? '✅' : '❌ Missing required file'
    });
  }

  // Check 2: Required directories exist
  const requiredDirs = ['demos', 'config', 'src', 'src/config', 'src/services'];
  
  for (const dir of requiredDirs) {
    const exists = await fs.pathExists(dir);
    checks.push({
      name: `Directory exists: ${dir}`,
      passed: exists,
      message: exists ? '✅' : '❌ Missing required directory'
    });
  }

  // Check 3: Environment file
  const envExists = await fs.pathExists('.env');
  checks.push({
    name: 'Environment configuration',
    passed: envExists,
    message: envExists ? '✅ .env file found' : '⚠️  .env file not found (copy from .env.example)'
  });

  // Check 4: Sample demo structure
  const sampleDemoPath = 'demos/sample-demo/documents';
  const sampleDemoExists = await fs.pathExists(sampleDemoPath);
  checks.push({
    name: 'Sample demo structure',
    passed: sampleDemoExists,
    message: sampleDemoExists ? '✅ Sample demo ready' : '✅ Sample demo structure created'
  });

  // Check 5: Dependencies
  const packageJson = await fs.readJson('package.json');
  const requiredDeps = [
    'express',
    'chromadb',
    'langchain',
    '@langchain/community',
    '@langchain/openai',
    'fs-extra',
    'cors',
    'dotenv'
  ];

  const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
  checks.push({
    name: 'Required dependencies',
    passed: missingDeps.length === 0,
    message: missingDeps.length === 0 ? '✅ All dependencies installed' : `❌ Missing: ${missingDeps.join(', ')}`
  });

  // Display results
  console.log('📋 Setup Validation Results:\n');
  
  let allPassed = true;
  for (const check of checks) {
    console.log(`${check.message} ${check.name}`);
    if (!check.passed) allPassed = false;
  }

  console.log('\n' + '='.repeat(50));
  
  if (allPassed) {
    console.log('🎉 Setup validation passed! Your server is ready to run.');
    console.log('\n📝 Next steps:');
    console.log('1. Copy .env.example to .env and configure your settings');
    console.log('2. Make sure Chroma is running at your configured URL');
    console.log('3. Add your OpenAI API key to .env');
    console.log('4. Start the server: npm start');
    console.log('5. Test the server: node test-server.js');
  } else {
    console.log('❌ Setup validation failed. Please fix the issues above.');
  }

  // Additional info
  console.log('\n📁 Project Structure:');
  await displayProjectStructure();
}

async function displayProjectStructure(dir = '.', level = 0, maxLevel = 2) {
  if (level > maxLevel) return;
  
  const items = await fs.readdir(dir);
  const indent = '  '.repeat(level);
  
  for (const item of items.sort()) {
    if (item.startsWith('.') && item !== '.env.example') continue;
    if (item === 'node_modules') continue;
    
    const itemPath = path.join(dir, item);
    const stat = await fs.stat(itemPath);
    
    if (stat.isDirectory()) {
      console.log(`${indent}📁 ${item}/`);
      await displayProjectStructure(itemPath, level + 1, maxLevel);
    } else {
      console.log(`${indent}📄 ${item}`);
    }
  }
}

// Run validation
if (require.main === module) {
  validateSetup().catch(console.error);
}

module.exports = validateSetup;

// Demo Configuration Types
export interface DemoConfig {
  demoName: string;
  collectionName: string;
  chromaUrl: string;
  createdAt: string;
  updatedAt: string;
  documentCount: number;
  chunkCount?: number;
  chunkSize: number;
  chunkOverlap: number;
  lastUpload?: string;
  lastCleared?: string;
}

export interface CreateDemoConfigOptions {
  collectionName?: string;
  chunkSize?: number;
  chunkOverlap?: number;
  chromaUrl?: string;
}

// API Request/Response Types
export interface UploadRequest {
  demoName: string;
  chunkSize?: number;
  chunkOverlap?: number;
  collectionName?: string;
}

export interface ClearRequest {
  demoName: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  demoName?: string;
  data?: T;
}

export interface UploadResponse extends ApiResponse {
  config?: {
    collectionName: string;
    chunkSize: number;
    chunkOverlap: number;
  };
  result?: ProcessingResult;
}

export interface ClearResponse extends ApiResponse {
  collectionName?: string;
  result?: ChromaClearResult;
}

// Document Processing Types
export interface ProcessingResult {
  success: boolean;
  collectionName: string;
  documentCount: number;
  originalDocuments: number;
  chunks: number;
  message: string;
}

export interface DocumentMetadata {
  demoName: string;
  documentIndex: number;
  processedAt: string;
  chunkIndex?: number;
  chunkSize?: number;
  source?: string;
  [key: string]: any;
}

// Chroma Service Types
export interface ChromaClearResult {
  success: boolean;
  collectionName: string;
  deletedCount: number;
  message: string;
}

export interface ChromaCollectionInfo {
  name: string;
  documentCount: number;
  exists: boolean;
}

export interface ChromaHealthCheck {
  status: 'healthy' | 'unhealthy';
  url: string;
  error?: string;
  timestamp: string;
}

// Demo Management Types
export interface DemoInfo {
  name: string;
  hasDocuments: boolean;
  config: DemoConfig | null;
  collectionInfo: ChromaCollectionInfo | null;
}

export interface DemosListResponse extends ApiResponse {
  demos?: DemoInfo[];
}

// Health Check Types
export interface HealthCheckResponse {
  status: 'ok' | 'error';
  timestamp: string;
  chroma: ChromaHealthCheck;
  message?: string;
}

// Error Types
export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

// Environment Variables
export interface EnvironmentConfig {
  CHROMA_URL: string;
  OPENAI_API_KEY: string;
  PORT: string;
}

// File Processing Types
export interface SupportedFileExtensions {
  '.txt': boolean;
  '.md': boolean;
  '.pdf': boolean;
  '.docx': boolean;
}

export type FileExtension = keyof SupportedFileExtensions;

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Express Request Extensions
export interface TypedRequest<T = any> extends Express.Request {
  body: T;
}

export interface TypedResponse<T = any> extends Express.Response {
  json: (body: T) => this;
  status: (code: number) => this;
}

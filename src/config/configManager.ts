import * as fs from 'fs-extra';
import * as path from 'path';
import { DemoConfig, CreateDemoConfigOptions } from '../types';

export class ConfigManager {
  private readonly configDir: string;

  constructor() {
    this.configDir = path.join(process.cwd(), 'config');
    this.ensureConfigDir();
  }

  private ensureConfigDir(): void {
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
  }

  private getConfigPath(demoName: string): string {
    return path.join(this.configDir, `${demoName}.json`);
  }

  async createDemoConfig(demoName: string, options: CreateDemoConfigOptions = {}): Promise<DemoConfig> {
    const config: DemoConfig = {
      demoName,
      collectionName: options.collectionName || `demo_${demoName}_${Date.now()}`,
      chromaUrl: options.chromaUrl || process.env.CHROMA_URL || 'http://localhost:8000',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      documentCount: 0,
      chunkSize: options.chunkSize || 1000,
      chunkOverlap: options.chunkOverlap || 200,
      ...options
    };

    const configPath = this.getConfigPath(demoName);
    await fs.writeJson(configPath, config, { spaces: 2 });
    return config;
  }

  async getDemoConfig(demoName: string): Promise<DemoConfig> {
    const configPath = this.getConfigPath(demoName);

    if (!await fs.pathExists(configPath)) {
      throw new Error(`Configuration for demo '${demoName}' not found`);
    }

    return await fs.readJson(configPath) as DemoConfig;
  }

  async updateDemoConfig(demoName: string, updates: Partial<DemoConfig>): Promise<DemoConfig> {
    const config = await this.getDemoConfig(demoName);
    const updatedConfig: DemoConfig = {
      ...config,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    const configPath = this.getConfigPath(demoName);
    await fs.writeJson(configPath, updatedConfig, { spaces: 2 });
    return updatedConfig;
  }

  async deleteDemoConfig(demoName: string): Promise<boolean> {
    const configPath = this.getConfigPath(demoName);
    if (await fs.pathExists(configPath)) {
      await fs.remove(configPath);
      return true;
    }
    return false;
  }

  async listDemoConfigs(): Promise<DemoConfig[]> {
    const files = await fs.readdir(this.configDir);
    const configFiles = files.filter(file => file.endsWith('.json'));

    const configs: DemoConfig[] = [];
    for (const file of configFiles) {
      const demoName = path.basename(file, '.json');
      try {
        const config = await this.getDemoConfig(demoName);
        configs.push(config);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.warn(`Failed to read config for ${demoName}:`, errorMessage);
      }
    }

    return configs;
  }

  async configExists(demoName: string): Promise<boolean> {
    const configPath = this.getConfigPath(demoName);
    return await fs.pathExists(configPath);
  }
}

import * as fs from 'fs-extra';
import * as path from 'path';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { DirectoryLoader } from 'langchain/document_loaders/fs/directory';
import { TextLoader } from 'langchain/document_loaders/fs/text';
import { OpenAIEmbeddings } from '@langchain/openai';
import { ChromaClient } from 'chromadb';
import { Document } from 'langchain/document';
import { DemoConfig, ProcessingResult, DocumentMetadata } from '../types';
import { Chroma } from '@langchain/community/vectorstores/chroma';

export class DocumentProcessor {
  private readonly embeddings: OpenAIEmbeddings;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY,
    });
  }

  async loadDocumentsFromDemo(demoName: string): Promise<Document<DocumentMetadata>[]> {
    const demoPath = path.join(process.cwd(), 'demos', demoName, 'documents');

    if (!await fs.pathExists(demoPath)) {
      throw new Error(`Demo documents directory not found: ${demoPath}`);
    }

    // Check if directory has any files
    const files = await fs.readdir(demoPath);
    if (files.length === 0) {
      throw new Error(`No documents found in demo '${demoName}'`);
    }

    // Create a directory loader with support for multiple file types
    const loader = new DirectoryLoader(demoPath, {
      '.txt': (filePath: string) => new TextLoader(filePath),
      '.md': (filePath: string) => new TextLoader(filePath),
      // Note: PDF and DOCX loaders require additional setup
      // '.pdf': (filePath: string) => new PDFLoader(filePath),
      // '.docx': (filePath: string) => new DocxLoader(filePath),
    });

    const documents = await loader.load();

    if (documents.length === 0) {
      throw new Error(`No supported documents found in demo '${demoName}'. Supported formats: .txt, .md`);
    }

    // Add metadata to documents
    const typedDocuments: Document<DocumentMetadata>[] = documents.map((doc, index) => ({
      ...doc,
      metadata: {
        ...doc.metadata,
        demoName,
        documentIndex: index,
        processedAt: new Date().toISOString(),
      } as DocumentMetadata
    }));

    return typedDocuments;
  }

  async splitDocuments(
    documents: Document<DocumentMetadata>[],
    chunkSize: number = 1000,
    chunkOverlap: number = 200
  ): Promise<Document<DocumentMetadata>[]> {
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize,
      chunkOverlap,
    });

    const splitDocs = await textSplitter.splitDocuments(documents);

    // Add chunk metadata
    const typedSplitDocs: Document<DocumentMetadata>[] = splitDocs.map((doc, index) => ({
      ...doc,
      metadata: {
        ...doc.metadata,
        chunkIndex: index,
        chunkSize: doc.pageContent.length,
      } as DocumentMetadata
    }));

    return typedSplitDocs;
  }

  async uploadToChroma(
    documents: Document<DocumentMetadata>[],
    config: DemoConfig
  ): Promise<ProcessingResult> {
    try {
      // Initialize Chroma client
      const chromaClient = new ChromaClient({
        path: config.chromaUrl,
      });

      const ch = Chroma(this.embeddings, {
        collectionName: config.collectionName,
        url: config.chromaUrl,
      });
      // Create or get collection with explicit embedding function
      let collection;
      try {
        collection = await chromaClient.getCollection({
          name: config.collectionName,
        });
        console.log(`Using existing collection: ${config.collectionName}`);
      } catch (error) {
        // Create collection without embedding function (we'll handle embeddings manually)
        collection = await chromaClient.createCollection({
          name: config.collectionName,
          metadata: { description: `Collection for demo: ${config.demoName}` }
        });
        console.log(`Created new collection: ${config.collectionName}`);
      }

      // Generate embeddings manually using OpenAI
      const texts = documents.map(doc => doc.pageContent);
      const embeddings = await this.embeddings.embedDocuments(texts);

      // Prepare data for Chroma
      const ids = documents.map((_, index) => `${config.demoName}_${Date.now()}_${index}`);
      const metadatas = documents.map(doc => ({
        ...doc.metadata,
        source: doc.metadata.source || 'unknown',
        demoName: config.demoName
      }));

      // Add documents to collection
      await collection.add({
        ids,
        embeddings,
        documents: texts,
        metadatas
      });

      console.log(`Successfully uploaded ${documents.length} documents to collection: ${config.collectionName}`);

      return {
        success: true,
        collectionName: config.collectionName,
        documentCount: documents.length,
        originalDocuments: 0, // Will be set by caller
        chunks: documents.length,
        message: `Successfully uploaded ${documents.length} document chunks to Chroma collection '${config.collectionName}'`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error uploading to Chroma:', error);

      // Check for specific error types
      if (errorMessage.includes('DefaultEmbeddingFunction') || errorMessage.includes('default-embed')) {
        throw new Error(`Failed to upload documents to Chroma: Missing embedding configuration. Make sure OPENAI_API_KEY is set and valid.`);
      } else if (errorMessage.includes('OPENAI_API_KEY')) {
        throw new Error(`Failed to upload documents to Chroma: OpenAI API key is invalid or missing.`);
      } else if (errorMessage.includes('Connection')) {
        throw new Error(`Failed to upload documents to Chroma: Cannot connect to Chroma server at ${config.chromaUrl}`);
      }

      throw new Error(`Failed to upload documents to Chroma: ${errorMessage}`);
    }
  }

  async processAndUpload(demoName: string, config: DemoConfig): Promise<ProcessingResult> {
    try {
      console.log(`Starting document processing for demo: ${demoName}`);

      // Load documents
      const documents = await this.loadDocumentsFromDemo(demoName);
      console.log(`Loaded ${documents.length} documents`);

      // Split documents into chunks
      const splitDocs = await this.splitDocuments(
        documents,
        config.chunkSize,
        config.chunkOverlap
      );
      console.log(`Split into ${splitDocs.length} chunks`);

      // Upload to Chroma
      const result = await this.uploadToChroma(splitDocs, config);

      return {
        ...result,
        originalDocuments: documents.length,
        chunks: splitDocs.length,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error processing documents for demo ${demoName}:`, errorMessage);
      throw error;
    }
  }
}

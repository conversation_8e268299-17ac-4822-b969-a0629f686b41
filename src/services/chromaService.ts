import { ChromaClient } from 'chromadb';
import { ChromaClearResult, ChromaCollectionInfo, ChromaHealthCheck } from '../types';

export class ChromaService {
  private readonly chromaUrl: string;
  private readonly client: ChromaClient;

  constructor(chromaUrl?: string) {
    this.chromaUrl = chromaUrl || process.env.CHROMA_URL || 'http://localhost:8000';
    this.client = new ChromaClient({
      path: this.chromaUrl,
    });
  }

  async clearCollection(collectionName: string): Promise<ChromaClearResult> {
    try {
      console.log(`Attempting to clear collection: ${collectionName}`);

      // Get the collection
      const collection = await this.client.getCollection({
        name: collectionName,
      });

      // Get all documents in the collection
      const result = await collection.get();

      if (result.ids && result.ids.length > 0) {
        // Delete all documents
        await collection.delete({
          ids: result.ids,
        });

        console.log(`Cleared ${result.ids.length} documents from collection '${collectionName}'`);

        return {
          success: true,
          collectionName,
          deletedCount: result.ids.length,
          message: `Successfully cleared ${result.ids.length} documents from collection '${collectionName}'`
        };
      } else {
        return {
          success: true,
          collectionName,
          deletedCount: 0,
          message: `Collection '${collectionName}' was already empty`
        };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error clearing collection ${collectionName}:`, error);

      if (errorMessage.includes('does not exist')) {
        throw new Error(`Collection '${collectionName}' does not exist`);
      }

      throw new Error(`Failed to clear collection '${collectionName}': ${errorMessage}`);
    }
  }

  async deleteCollection(collectionName: string): Promise<{ success: boolean; collectionName: string; message: string }> {
    try {
      console.log(`Attempting to delete collection: ${collectionName}`);

      await this.client.deleteCollection({
        name: collectionName,
      });

      console.log(`Deleted collection '${collectionName}'`);

      return {
        success: true,
        collectionName,
        message: `Successfully deleted collection '${collectionName}'`
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error deleting collection ${collectionName}:`, error);

      if (errorMessage.includes('does not exist')) {
        throw new Error(`Collection '${collectionName}' does not exist`);
      }

      throw new Error(`Failed to delete collection '${collectionName}': ${errorMessage}`);
    }
  }

  async listCollections(): Promise<any[]> {
    try {
      const collections = await this.client.listCollections();
      return collections;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error listing collections:', error);
      throw new Error(`Failed to list collections: ${errorMessage}`);
    }
  }

  async getCollectionInfo(collectionName: string): Promise<ChromaCollectionInfo> {
    try {
      const collection = await this.client.getCollection({
        name: collectionName,
      });

      const result = await collection.get();

      return {
        name: collectionName,
        documentCount: result.ids ? result.ids.length : 0,
        exists: true
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('does not exist')) {
        return {
          name: collectionName,
          documentCount: 0,
          exists: false
        };
      }

      throw new Error(`Failed to get collection info for '${collectionName}': ${errorMessage}`);
    }
  }

  async healthCheck(): Promise<ChromaHealthCheck> {
    try {
      await this.client.heartbeat();
      return {
        status: 'healthy',
        url: this.chromaUrl,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        status: 'unhealthy',
        url: this.chromaUrl,
        error: errorMessage,
        timestamp: new Date().toISOString()
      };
    }
  }
}

{"name": "demos-rag", "version": "1.0.0", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node server.ts", "dev:watch": "nodemon --exec ts-node server.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "postbuild": "cp -r demos dist/ && cp .env dist/ || true", "type-check": "tsc --noEmit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["rag", "chroma", "langchain", "document-processing"], "author": "", "license": "ISC", "description": "A Node.js server for uploading and managing documents in Chroma vector database for RAG applications", "dependencies": {"@langchain/community": "^0.3.47", "@langchain/openai": "^0.5.16", "chromadb": "^3.0.5", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "langchain": "^0.3.29", "multer": "^2.0.1", "path": "^0.12.7"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/fs-extra": "^11.0.4", "@types/node": "^24.0.7", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
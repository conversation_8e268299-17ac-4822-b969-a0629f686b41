{"name": "demos-rag", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["rag", "chroma", "langchain", "document-processing"], "author": "", "license": "ISC", "description": "A Node.js server for uploading and managing documents in Chroma vector database for RAG applications", "dependencies": {"@langchain/community": "^0.3.47", "@langchain/openai": "^0.5.16", "chromadb": "^3.0.5", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "fs-extra": "^11.3.0", "langchain": "^0.3.29", "multer": "^2.0.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.1.10"}}
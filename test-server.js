const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testServer() {
  console.log('🧪 Testing Demos RAG Server...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data.status);
    console.log('   Chroma status:', healthResponse.data.chroma.status);
    console.log();

    // Test 2: List Demos
    console.log('2. Testing demos list endpoint...');
    const demosResponse = await axios.get(`${BASE_URL}/demos`);
    console.log('✅ Demos list retrieved:', demosResponse.data.demos.length, 'demos found');
    if (demosResponse.data.demos.length > 0) {
      console.log('   Available demos:', demosResponse.data.demos.map(d => d.name).join(', '));
    }
    console.log();

    // Test 3: Upload (if sample-demo exists)
    const sampleDemo = demosResponse.data.demos.find(d => d.name === 'sample-demo');
    if (sampleDemo && sampleDemo.hasDocuments) {
      console.log('3. Testing upload endpoint with sample-demo...');
      try {
        const uploadResponse = await axios.post(`${BASE_URL}/upload`, {
          demoName: 'sample-demo',
          chunkSize: 500,
          chunkOverlap: 100
        });
        console.log('✅ Upload successful!');
        console.log('   Collection:', uploadResponse.data.config.collectionName);
        console.log('   Documents processed:', uploadResponse.data.result.originalDocuments);
        console.log('   Chunks created:', uploadResponse.data.result.chunks);
        console.log();

        // Test 4: Clear
        console.log('4. Testing clear endpoint...');
        const clearResponse = await axios.post(`${BASE_URL}/clear`, {
          demoName: 'sample-demo'
        });
        console.log('✅ Clear successful!');
        console.log('   Documents cleared:', clearResponse.data.result.deletedCount);
        console.log();
      } catch (uploadError) {
        console.log('❌ Upload test failed:', uploadError.response?.data?.message || uploadError.message);
        console.log();
      }
    } else {
      console.log('3. Skipping upload test - no sample-demo with documents found');
      console.log('   To test upload, create: demos/sample-demo/documents/sample.txt');
      console.log();
    }

    console.log('🎉 Server testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the server is running:');
      console.log('   npm start');
    }
    
    if (error.response?.data?.chroma?.status === 'unhealthy') {
      console.log('\n💡 Make sure Chroma is running and accessible at:', error.response.data.chroma.url);
    }
  }
}

// Add axios as a dependency check
async function checkDependencies() {
  try {
    require('axios');
  } catch (error) {
    console.log('Installing axios for testing...');
    const { exec } = require('child_process');
    await new Promise((resolve, reject) => {
      exec('npm install axios --no-save', (error, stdout, stderr) => {
        if (error) reject(error);
        else resolve();
      });
    });
  }
}

// Run tests
if (require.main === module) {
  checkDependencies()
    .then(() => testServer())
    .catch(console.error);
}

module.exports = testServer;
